# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.51"
name = "arrayvec"
version = "0.7.6"
authors = ["bluss"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A vector with fixed capacity, backed by an array (it can be stored on the stack too). Implements fixed capacity ArrayVec and ArrayString."
documentation = "https://docs.rs/arrayvec/"
readme = "README.md"
keywords = [
    "stack",
    "vector",
    "array",
    "data-structure",
    "no_std",
]
categories = [
    "data-structures",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/bluss/arrayvec"

[package.metadata.docs.rs]
features = [
    "borsh",
    "serde",
    "zeroize",
]

[package.metadata.release]
no-dev-version = true
tag-name = "{{version}}"

[profile.bench]
debug = 2

[profile.release]
debug = 2

[lib]
name = "arrayvec"
path = "src/lib.rs"

[[test]]
name = "borsh"
path = "tests/borsh.rs"

[[test]]
name = "serde"
path = "tests/serde.rs"

[[test]]
name = "tests"
path = "tests/tests.rs"

[[bench]]
name = "arraystring"
path = "benches/arraystring.rs"
harness = false

[[bench]]
name = "extend"
path = "benches/extend.rs"
harness = false

[dependencies.borsh]
version = "1.2.0"
optional = true
default-features = false

[dependencies.serde]
version = "1.0"
optional = true
default-features = false

[dependencies.zeroize]
version = "1.4"
optional = true
default-features = false

[dev-dependencies.bencher]
version = "0.1.4"

[dev-dependencies.matches]
version = "0.1"

[dev-dependencies.serde_test]
version = "1.0"

[build-dependencies]

[features]
default = ["std"]
std = []
