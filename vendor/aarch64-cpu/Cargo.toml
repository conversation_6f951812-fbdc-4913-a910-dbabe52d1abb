# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "aarch64-cpu"
version = "9.4.0"
authors = ["<PERSON> <<EMAIL>>"]
exclude = [
    ".github",
    ".gitignore",
    ".rustfmt.toml",
    ".vscode",
    ".editorconfig",
    "Makefile",
]
description = "Low level access to processors using the AArch64 execution state"
homepage = "https://github.com/rust-embedded/aarch64-cpu"
readme = "README.md"
keywords = [
    "arm",
    "aarch64",
    "cpu",
    "register",
]
categories = [
    "embedded",
    "hardware-support",
    "no-std",
]
license = "MIT/Apache-2.0"
repository = "https://github.com/rust-embedded/aarch64-cpu"

[dependencies.tock-registers]
version = "0.8.x"
default-features = false
