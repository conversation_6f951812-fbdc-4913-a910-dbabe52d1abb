# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "arm_pl011"
version = "0.1.0"
authors = [
    "Shiping Yuan <<EMAIL>>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "ARM Uart pl011 register definitions and basic operations"
homepage = "https://github.com/arceos-org/arceos"
documentation = "https://docs.rs/arm_pl011"
readme = "README.md"
keywords = [
    "arm",
    "pl011",
    "uart",
    "arceos",
]
categories = [
    "embedded",
    "no-std",
    "hardware-support",
    "os",
]
license = "GPL-3.0-or-later OR Apache-2.0 OR MulanPSL-2.0"
repository = "https://github.com/arceos-org/arm_pl011"

[lib]
name = "arm_pl011"
path = "src/lib.rs"

[dependencies.tock-registers]
version = "0.8"
