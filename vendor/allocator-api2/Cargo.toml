# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "allocator-api2"
version = "0.2.16"
authors = ["Zakarum <<EMAIL>>"]
description = "Mirror of Rust's allocator API"
homepage = "https://github.com/zakarumych/allocator-api2"
documentation = "https://docs.rs/allocator-api2"
readme = "README.md"
license = "MIT OR Apache-2.0"
repository = "https://github.com/zakarumych/allocator-api2"

[dependencies.serde]
version = "1.0"
optional = true

[features]
alloc = []
default = ["std"]
nightly = []
std = ["alloc"]
