# arm_gicv2

[![Crates.io](https://img.shields.io/crates/v/arm_gicv2)](https://crates.io/crates/arm_gicv2)
[![Docs.rs](https://docs.rs/arm_gicv2/badge.svg)](https://docs.rs/arm_gicv2)
[![CI](https://github.com/arceos-org/arm_gicv2/actions/workflows/ci.yml/badge.svg?branch=main)](https://github.com/arceos-org/arm_gicv2/actions/workflows/ci.yml)

ARM Generic Interrupt Controller version 2 (GICv2) register definitions and basic operations.

The official documentation: <https://developer.arm.com/documentation/ihi0048/latest/>
